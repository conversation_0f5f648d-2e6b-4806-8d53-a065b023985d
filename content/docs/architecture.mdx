# Architecture Overview

## System Components

### Frontend Layer

| **React.js Application** |
|:------------------------|
| • Wallet Interface      |
| • Trading Interface     |
| • Swap Interface        |
| • Market Interface      |
| • Analytics Tools       |

### Backend Layer

| **Node.js API Server**  |
|:------------------------|
| • Authentication        |
| • Data Aggregation      |
| • Order Management      |
| • Risk Engine           |

### Blockchain Layer

| **HyperCore**           |
|:------------------------|
| • Order Books           |
| • Spot Trading          |
| • Perpetuals            |

| **HyperEVM**            |
|:------------------------|
| • Smart Contracts       |
| • DeFi Protocols        |
| • Cross-Chain Bridges   |

## System Flow

**Frontend Layer** → **Backend Layer** → **Blockchain Layer**

The Purro architecture follows a traditional three-tier model with clear separation of concerns:

1. **Frontend Layer** handles user interactions and data visualization
2. **Backend Layer** manages business logic, data processing, and security
3. **Blockchain Layer** provides the underlying infrastructure for transactions and smart contracts