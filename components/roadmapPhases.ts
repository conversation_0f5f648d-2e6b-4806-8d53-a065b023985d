export const roadmapPhases = [
    {
        phase: "Phase 1",
        title: "Foundation & Setup",
        status: "in-progress",
        description: "Project foundation, documentation, and community building",
        tasks: [
            {
                name: "Project Planning & Roadmap",
                status: "in-progress",
                category: "planning",
            },
            {
                name: "Whitepaper & Documentation",
                status: "in-progress",
                category: "docs",
            },
            {
                name: "Brand Identity & Design System",
                status: "completed",
                category: "design",
            },
            {
                name: "Website Development",
                status: "in-progress",
                category: "website",
            },
            {
                name: "Discord Community Setup",
                status: "completed",
                category: "community",
            },
            {
                name: "Social Media Presence",
                status: "completed",
                category: "community",
            },
            {
                name: "Developer Documentation",
                status: "planned",
                category: "docs",
            },
        ],
        deliverables: "Complete project foundation with community and documentation",
    },
    {
        phase: "Phase 2",
        title: "Token Launch & Community",
        status: "in-progress",
        description: "PURRO token launch, airdrop campaign, and business development",
        tasks: [
            {
                name: "PURRO Token Launch",
                status: "completed",
                category: "mvp",
            },
            {
                name: "Airdrop Campaign",
                status: "planned",
                category: "community",
            },
            {
                name: "Business Development",
                status: "planned",
                category: "community",
            },
            {
                name: "X Bot Development",
                status: "in-progress",
                category: "bot",
            },
            {
                name: "Community Growth",
                status: "planned",
                category: "community",
            },
            {
                name: "Marketing Campaigns",
                status: "planned",
                category: "community",
            },
            {
                name: "Partnership Outreach",
                status: "in-progress",
                category: "community",
            },
        ],
        deliverables: "Live token with active community and strong partnerships",
    },
    {
        phase: "Phase 3",
        title: "MVP Development",
        status: "in-progress",
        description: "Core platform development and basic functionality",
        tasks: [
            {
                name: "HyperCore SDK Integration",
                status: "in-progress",
                category: "mvp",
            },
            {
                name: "Basic Trading Interface",
                status: "in-progress",
                category: "mvp",
            },
            {
                name: "Wallet System",
                status: "in-progress",
                category: "mvp",
            },
            {
                name: "Database Schema Design",
                status: "completed",
                category: "mvp",
            },
            {
                name: "API Development",
                status: "in-progress",
                category: "mvp",
            },
            {
                name: "Beta Testing Environment",
                status: "completed",
                category: "mvp",
            },
            {
                name: "Security Framework",
                status: "in-progress",
                category: "mvp",
            },
            {
                name: "Smart Contract Architecture",
                status: "planned",
                category: "mvp",
            },
        ],
        deliverables: "Working MVP with basic DeFi hub functionality",
    },
    {
        phase: "Phase 4",
        title: "Platform Launch",
        status: "planned",
        description: "Public launch, user acquisition, and feature expansion",
        tasks: [
            {
                name: "Smart Contract Deployment",
                status: "planned",
                category: "mvp",
            },
            {
                name: "Security Audit & Bug Fixes",
                status: "planned",
                category: "mvp",
            },
            {
                name: "Public Beta Launch",
                status: "planned",
                category: "mvp",
            },
            {
                name: "Advanced Trading Bots",
                status: "planned",
                category: "bot",
            },
            {
                name: "Analytics Dashboard",
                status: "planned",
                category: "website",
            },
            {
                name: "User Onboarding System",
                status: "planned",
                category: "website",
            },
            {
                name: "Community Ambassador Program",
                status: "planned",
                category: "community",
            },
            {
                name: "Institutional Partnerships",
                status: "planned",
                category: "community",
            },
        ],
        deliverables: "Live platform with active user base and trading functionality",
    },
    {
        phase: "Phase 5",
        title: "Advanced Features & Scale",
        status: "future",
        description: "Enhanced functionality, aggregation, and ecosystem expansion",
        tasks: [
            {
                name: "HyperEVM Smart Contracts",
                status: "future",
                category: "mvp",
            },
            {
                name: "DEX Aggregation Engine",
                status: "future",
                category: "mvp",
            },
            {
                name: "Advanced Order Types",
                status: "future",
                category: "mvp",
            },
            {
                name: "Mobile App Development",
                status: "future",
                category: "website",
            },
            {
                name: "AI Trading Bots",
                status: "future",
                category: "bot",
            },
            {
                name: "DAO Governance System",
                status: "future",
                category: "mvp",
            },
            {
                name: "Cross-chain Integration",
                status: "future",
                category: "mvp",
            },
            {
                name: "Global Community Expansion",
                status: "future",
                category: "community",
            },
        ],
        deliverables: "Complete DeFi ecosystem with governance and global reach",
    },
];