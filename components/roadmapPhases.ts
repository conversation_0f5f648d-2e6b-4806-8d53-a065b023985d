export const roadmapPhases = [
  {
    phase: "Phase 1",
    title: "Foundation & Setup",
    status: "in-progress",
    description: "Project foundation, documentation, and community building",
    tasks: [
      {
        name: "Project Planning & Roadmap",
        status: "in-progress",
        category: "planning",
      },
      {
        name: "Whitepaper & Documentation",
        status: "in-progress",
        category: "docs",
      },
      {
        name: "Brand Identity & Design System",
        status: "completed",
        category: "design",
      },
      {
        name: "Website Development",
        status: "in-progress",
        category: "website",
      },
      {
        name: "Discord Community Setup",
        status: "completed",
        category: "community",
      },
      {
        name: "Social Media Presence",
        status: "completed",
        category: "community",
      },
      {
        name: "Developer Documentation",
        status: "planned",
        category: "docs",
      },
    ],
    deliverables:
      "Complete project foundation with community and documentation",
  },
  {
    phase: "Phase 2",
    title: "DeFi Hub Development",
    status: "in-progress",
    description:
      "Core DeFi Hub platform development - our primary component and initial focus",
    tasks: [
      {
        name: "PURRO Token Launch",
        status: "completed",
        category: "mvp",
      },
      {
        name: "HyperCore SDK Integration",
        status: "in-progress",
        category: "mvp",
      },
      {
        name: "Basic Trading Interface",
        status: "in-progress",
        category: "mvp",
      },
      {
        name: "Wallet System",
        status: "in-progress",
        category: "mvp",
      },
      {
        name: "Database Schema Design",
        status: "completed",
        category: "mvp",
      },
      {
        name: "API Development",
        status: "in-progress",
        category: "mvp",
      },
      {
        name: "Beta Testing Environment",
        status: "completed",
        category: "mvp",
      },
      {
        name: "Security Framework",
        status: "in-progress",
        category: "mvp",
      },
    ],
    deliverables:
      "Working DeFi Hub MVP with basic aggregation and trading functionality",
  },
  {
    phase: "Phase 3",
    title: "X Bot - Market Intelligence",
    status: "in-progress",
    description:
      "X Bot development - advanced market intelligence evolved from HyperBaby AI",
    tasks: [
      {
        name: "X Bot Core Development",
        status: "in-progress",
        category: "bot",
      },
      {
        name: "Market Data Integration",
        status: "in-progress",
        category: "bot",
      },
      {
        name: "AI Analysis Engine",
        status: "planned",
        category: "bot",
      },
      {
        name: "Trading Signal Generation",
        status: "planned",
        category: "bot",
      },
      {
        name: "Portfolio Analytics",
        status: "planned",
        category: "bot",
      },
      {
        name: "Real-time Notifications",
        status: "planned",
        category: "bot",
      },
      {
        name: "HyperBaby AI Migration",
        status: "completed",
        category: "bot",
      },
      {
        name: "Bot Testing & Optimization",
        status: "planned",
        category: "bot",
      },
    ],
    deliverables:
      "Fully functional X Bot with market intelligence and trading insights",
  },
  {
    phase: "Phase 4",
    title: "DeFi Hub Launch",
    status: "planned",
    description:
      "Public launch of DeFi Hub with full functionality and user acquisition",
    tasks: [
      {
        name: "Smart Contract Deployment",
        status: "planned",
        category: "mvp",
      },
      {
        name: "Security Audit & Bug Fixes",
        status: "planned",
        category: "mvp",
      },
      {
        name: "DeFi Hub Public Launch",
        status: "planned",
        category: "mvp",
      },
      {
        name: "Liquidity Aggregation Live",
        status: "planned",
        category: "mvp",
      },
      {
        name: "Analytics Dashboard",
        status: "planned",
        category: "website",
      },
      {
        name: "User Onboarding System",
        status: "planned",
        category: "website",
      },
      {
        name: "Community Growth Campaign",
        status: "planned",
        category: "community",
      },
      {
        name: "Partnership Integration",
        status: "planned",
        category: "community",
      },
    ],
    deliverables:
      "Live DeFi Hub with active user base and full aggregation functionality",
  },
  {
    phase: "Phase 5",
    title: "HyperEVM DEX Aggregator",
    status: "future",
    description:
      "Development and launch of HyperEVM DEX Aggregator - the final core component",
    tasks: [
      {
        name: "HyperEVM Smart Contracts",
        status: "future",
        category: "mvp",
      },
      {
        name: "DEX Aggregation Engine",
        status: "future",
        category: "mvp",
      },
      {
        name: "Cross-DEX Liquidity Routing",
        status: "future",
        category: "mvp",
      },
      {
        name: "Optimal Routing Algorithms",
        status: "future",
        category: "mvp",
      },
      {
        name: "Slippage Minimization",
        status: "future",
        category: "mvp",
      },
      {
        name: "Advanced Order Types",
        status: "future",
        category: "mvp",
      },
      {
        name: "Mobile App Development",
        status: "future",
        category: "website",
      },
      {
        name: "DAO Governance System",
        status: "future",
        category: "mvp",
      },
      {
        name: "Global Ecosystem Expansion",
        status: "future",
        category: "community",
      },
    ],
    deliverables:
      "Complete three-component DeFi ecosystem with full aggregation capabilities",
  },
];
